# Simple Desktop 打包说明

本文档说明如何将 Simple Desktop 应用程序打包成可执行文件。

## 文件说明

### 打包脚本
- `setup.py` - 基础打包脚本
- `build_advanced.py` - 高级打包脚本，支持多种选项
- `SimpleDesktop.spec` - PyInstaller 配置文件

### 批处理文件
- `build.bat` - 运行基础打包脚本
- `build_advanced.bat` - 运行高级打包脚本（推荐）

## 使用方法

### 方法一：使用高级打包脚本（推荐）

1. 双击运行 `build_advanced.bat`
2. 选择构建类型：
   - **单文件版本**：生成单个 .exe 文件，启动稍慢但便于分发
   - **目录版本**：生成包含多个文件的目录，启动快但文件较多
   - **spec文件构建**：使用预配置的 spec 文件构建

### 方法二：使用基础打包脚本

1. 双击运行 `build.bat`
2. 等待构建完成

### 方法三：命令行方式

```bash
# 单文件版本
python build_advanced.py --type onefile

# 目录版本
python build_advanced.py --type onedir

# 使用spec文件
python build_advanced.py --type spec
```

## 构建要求

### 系统要求
- Windows 10/11
- Python 3.8+
- 已安装项目依赖（PySide6, pywin32）

### 必需文件
- `D:\withEverything\ic.ico` - 应用程序图标
- `Everything-SDK/dll/Everything64.dll` - Everything 搜索引擎
- `simple_desktop/` - 应用程序源代码目录

## 输出文件

构建完成后，在 `dist/` 目录下会生成：

### 单文件版本
- `SimpleDesktop.exe` - 主程序
- `Everything64.dll` - Everything 搜索引擎
- `ic.ico` - 图标文件
- `install.bat` - 安装脚本

### 目录版本
- `SimpleDesktop/` - 程序目录
  - `SimpleDesktop.exe` - 主程序
  - 其他依赖文件...
- `Everything64.dll` - Everything 搜索引擎
- `ic.ico` - 图标文件
- `install.bat` - 安装脚本
- `SimpleDesktop_Portable_YYYYMMDD_HHMMSS.zip` - 便携版压缩包

## 安装和使用

### 直接运行
直接双击 `SimpleDesktop.exe` 即可运行程序。

### 系统安装
1. 运行 `install.bat` 安装脚本
2. 程序将安装到 `C:\Program Files\SimpleDesktop\`
3. 自动创建桌面和开始菜单快捷方式

### 便携版
解压 `SimpleDesktop_Portable_*.zip` 到任意目录即可使用。

## 故障排除

### 常见问题

1. **图标文件不存在**
   - 确保 `D:\withEverything\ic.ico` 文件存在
   - 或修改脚本中的图标路径

2. **Everything64.dll 缺失**
   - 确保 `Everything-SDK/dll/Everything64.dll` 文件存在
   - 从 Everything 官网下载 SDK

3. **构建失败**
   - 检查 Python 环境和依赖
   - 运行 `pip install -r requirements.txt`
   - 确保 PyInstaller 已安装

4. **程序无法启动**
   - 确保目标系统已安装 Visual C++ Redistributable
   - 检查 Everything 是否正在运行

### 调试模式

如需调试，可以修改 PyInstaller 参数：
- 移除 `--windowed` 参数以显示控制台
- 添加 `--debug=all` 参数获取详细日志

## 自定义配置

### 修改图标
编辑脚本中的图标路径：
```python
--icon=D:\\withEverything\\ic.ico
```

### 添加额外文件
在 `--add-data` 参数中添加：
```python
--add-data=source;destination
```

### 排除模块
使用 `--exclude-module` 参数减小文件大小：
```python
--exclude-module=tkinter
--exclude-module=matplotlib
```

## 版本信息

- 构建脚本版本：1.0
- 支持的 Python 版本：3.8+
- 支持的操作系统：Windows 10/11
- 依赖的主要库：PySide6, PyInstaller, pywin32
