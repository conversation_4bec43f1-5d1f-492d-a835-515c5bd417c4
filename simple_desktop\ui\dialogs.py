"""
对话框组件
包含文件类型筛选、路径管理、帮助等对话框
"""

import os
from typing import List, Dict
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QCheckBox, QPushButton, 
    QLabel, QListWidget, QListWidgetItem, QFileDialog, QTextEdit,
    QGroupBox, QGridLayout, QMessageBox, QScrollArea, QWidget
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont

from ..core.config import config_manager
from ..core.profile_manager import profile_manager
from ..search.engine import FileSearchEngine


class FileTypeFilterDialog(QDialog):
    """文件类型筛选对话框"""
    
    filter_changed = Signal(list)  # 筛选改变信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("文件类型筛选")
        self.setFixedSize(400, 500)
        self.setModal(True)
        
        # 文件类型定义
        self.file_types = {
            "documents": {
                "name": "文档",
                "extensions": [".txt", ".doc", ".docx", ".pdf", ".rtf", ".odt", ".xls", ".xlsx", ".ppt", ".pptx"],
                "icon": "📄"
            },
            "images": {
                "name": "图片",
                "extensions": [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".svg", ".ico", ".webp"],
                "icon": "🖼️"
            },
            "videos": {
                "name": "视频",
                "extensions": [".mp4", ".avi", ".mkv", ".mov", ".wmv", ".flv", ".webm", ".m4v", ".3gp"],
                "icon": "🎬"
            },
            "audio": {
                "name": "音频",
                "extensions": [".mp3", ".wav", ".flac", ".aac", ".ogg", ".wma", ".m4a"],
                "icon": "🎵"
            },
            "archives": {
                "name": "压缩包",
                "extensions": [".zip", ".rar", ".7z", ".tar", ".gz", ".bz2", ".xz"],
                "icon": "📦"
            },
            "executables": {
                "name": "可执行文件",
                "extensions": [".exe", ".msi", ".bat", ".cmd", ".com", ".scr"],
                "icon": "⚙️"
            },
            "code": {
                "name": "代码文件",
                "extensions": [".py", ".js", ".html", ".css", ".cpp", ".c", ".java", ".cs", ".php", ".rb", ".go"],
                "icon": "💻"
            },
            "folders": {
                "name": "文件夹",
                "extensions": [],
                "icon": "📁"
            }
        }
        
        # 获取当前选中的文件类型
        self.selected_types = config_manager.get_enabled_file_types()
        
        self.init_ui()
        self.load_current_selection()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout()
        
        # 标题
        title_label = QLabel("选择要显示的文件类型")
        title_label.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        
        # 文件类型复选框
        self.checkboxes = {}
        for type_key, type_info in self.file_types.items():
            checkbox = QCheckBox(f"{type_info['icon']} {type_info['name']}")
            checkbox.setFont(QFont("Microsoft YaHei", 10))
            
            # 添加扩展名信息
            if type_info['extensions']:
                ext_text = ", ".join(type_info['extensions'][:5])
                if len(type_info['extensions']) > 5:
                    ext_text += f" 等 {len(type_info['extensions'])} 种"
                checkbox.setToolTip(f"包含: {ext_text}")
            
            self.checkboxes[type_key] = checkbox
            scroll_layout.addWidget(checkbox)
        
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        scroll_area.setMaximumHeight(350)
        layout.addWidget(scroll_area)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        # 全选/全不选按钮
        select_all_btn = QPushButton("全选")
        select_all_btn.clicked.connect(self.select_all)
        
        select_none_btn = QPushButton("全不选")
        select_none_btn.clicked.connect(self.select_none)
        
        button_layout.addWidget(select_all_btn)
        button_layout.addWidget(select_none_btn)
        button_layout.addStretch()
        
        # 确定/取消按钮
        ok_btn = QPushButton("确定")
        ok_btn.clicked.connect(self.accept)
        
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        
        button_layout.addWidget(ok_btn)
        button_layout.addWidget(cancel_btn)
        
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
    
    def load_current_selection(self):
        """加载当前选择"""
        for type_key, checkbox in self.checkboxes.items():
            checkbox.setChecked(type_key in self.selected_types)
    
    def select_all(self):
        """全选"""
        for checkbox in self.checkboxes.values():
            checkbox.setChecked(True)
    
    def select_none(self):
        """全不选"""
        for checkbox in self.checkboxes.values():
            checkbox.setChecked(False)
    
    def get_selected_types(self) -> List[str]:
        """获取选中的文件类型"""
        selected = []
        for type_key, checkbox in self.checkboxes.items():
            if checkbox.isChecked():
                selected.append(type_key)
        return selected
    
    def accept(self):
        """确定按钮处理"""
        selected_types = self.get_selected_types()
        
        # 保存到配置
        config_manager.set_enabled_file_types(selected_types)
        
        # 发送信号
        self.filter_changed.emit(selected_types)
        
        super().accept()


class PathManagementDialog(QDialog):
    """路径管理对话框"""
    
    path_added = Signal(str)  # 路径添加信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("管理扫描目录")
        self.setFixedSize(500, 400)
        self.setModal(True)
        
        self.init_ui()
        self.load_current_paths()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout()
        
        # 标题
        title_label = QLabel(f"Profile {profile_manager.current_profile_id} - 扫描目录管理")
        title_label.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Bold))
        layout.addWidget(title_label)
        
        # 当前路径列表
        self.path_list = QListWidget()
        self.path_list.setMinimumHeight(250)
        layout.addWidget(self.path_list)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        add_btn = QPushButton("添加目录")
        add_btn.clicked.connect(self.add_directory)
        
        remove_btn = QPushButton("移除选中")
        remove_btn.clicked.connect(self.remove_selected)
        
        button_layout.addWidget(add_btn)
        button_layout.addWidget(remove_btn)
        button_layout.addStretch()
        
        # 关闭按钮
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.accept)
        
        button_layout.addWidget(close_btn)
        
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
    
    def load_current_paths(self):
        """加载当前路径"""
        self.path_list.clear()
        paths = profile_manager.get_profile_scan_directories()
        
        for path in paths:
            item = QListWidgetItem(path)
            item.setToolTip(path)
            self.path_list.addItem(item)
    
    def add_directory(self):
        """添加目录"""
        directory = QFileDialog.getExistingDirectory(
            self, 
            "选择要添加的目录",
            os.path.expanduser("~")
        )
        
        if directory:
            success = profile_manager.add_directory_to_current(directory)
            if success:
                self.load_current_paths()
                self.path_added.emit(directory)
                QMessageBox.information(self, "成功", f"已添加目录: {directory}")
            else:
                QMessageBox.warning(self, "警告", "目录已存在或添加失败")
    
    def remove_selected(self):
        """移除选中的目录"""
        current_item = self.path_list.currentItem()
        if current_item:
            directory = current_item.text()

            reply = QMessageBox.question(
                self,
                "确认删除",
                f"确定要移除目录吗？\n{directory}",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                success = profile_manager.remove_directory_from_profile(directory)
                if success:
                    self.load_current_paths()
                    # 发送路径变更信号（虽然是删除，但需要通知搜索窗口更新）
                    self.path_added.emit("")  # 空字符串表示路径配置已更改
                    QMessageBox.information(self, "成功", "目录已移除")
                else:
                    QMessageBox.warning(self, "错误", "移除目录失败")


class HelpDialog(QDialog):
    """帮助对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("操作方式和快捷键")
        self.setFixedSize(600, 500)
        self.setModal(True)
        
        self.init_ui()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout()
        
        # 标题
        title_label = QLabel("Simple Desktop - 操作指南")
        title_label.setFont(QFont("Microsoft YaHei", 14, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 帮助内容
        help_text = QTextEdit()
        help_text.setReadOnly(True)
        help_text.setHtml(self.get_help_content())
        layout.addWidget(help_text)
        
        # 关闭按钮
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.accept)
        close_btn.setFixedWidth(100)
        
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        button_layout.addWidget(close_btn)
        button_layout.addStretch()
        
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
    
    def get_help_content(self) -> str:
        """获取帮助内容"""
        return """
        <h3>🚀 快捷键</h3>
        <ul>
            <li><b>双击 Ctrl</b> - 显示/隐藏搜索窗口</li>
            <li><b>Ctrl + 0~9</b> - 快速切换到对应Profile</li>
            <li><b>Tab</b> - 切换打开文件/打开文件夹模式</li>
            <li><b>ESC</b> - 隐藏搜索窗口</li>
            <li><b>↑↓</b> - 在搜索结果中导航</li>
            <li><b>Enter</b> - 打开选中的文件/文件夹</li>
            <li><b>→</b> - 从搜索框切换到后缀框</li>
            <li><b>←</b> - 从后缀框切换到搜索框</li>
        </ul>
        
        <h3>🔍 搜索技巧</h3>
        <ul>
            <li><b>基础搜索</b> - 直接输入文件名关键词</li>
            <li><b>后缀过滤</b> - 在后缀框输入文件扩展名（如 .txt）</li>
            <li><b>文件类型</b> - 点击"类型"按钮选择要显示的文件类型</li>
            <li><b>Everything语法</b> - 支持Everything的高级搜索语法</li>
        </ul>
        
        <h3>📁 Profile管理</h3>
        <ul>
            <li><b>10个Profile</b> - 标签0-9，每个可配置不同的搜索范围</li>
            <li><b>独立配置</b> - 每个Profile有独立的扫描目录和文件类型过滤</li>
            <li><b>快速切换</b> - 使用Ctrl+数字键或点击标签切换</li>
        </ul>
        
        <h3>⚙️ 设置说明</h3>
        <ul>
            <li><b>目录管理</b> - 点击"目录"按钮管理当前Profile的扫描目录</li>
            <li><b>文件类型</b> - 点击"类型"按钮选择要显示的文件类型</li>
            <li><b>打开方式</b> - 点击右侧按钮或按Tab键切换打开文件/文件夹</li>
        </ul>
        
        <h3>💡 使用提示</h3>
        <ul>
            <li>搜索基于Everything引擎，需要Everything服务运行</li>
            <li>窗口可以被其他应用覆盖，支持多任务操作</li>
            <li>配置自动保存，重启后保持设置</li>
            <li>支持系统托盘，可从托盘重新显示窗口</li>
        </ul>
        """
