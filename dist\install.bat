@echo off
echo Simple Desktop 安装程序
echo.

set "INSTALL_DIR=%PROGRAMFILES%\SimpleDesktop"
set "DESKTOP_SHORTCUT=%USERPROFILE%\Desktop\SimpleDesktop.lnk"
set "STARTMENU_SHORTCUT=%APPDATA%\Microsoft\Windows\Start Menu\Programs\SimpleDesktop.lnk"

echo 正在安装到: %INSTALL_DIR%
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

copy "SimpleDesktop.exe" "%INSTALL_DIR%\"
copy "Everything64.dll" "%INSTALL_DIR%\"
copy "ic.ico" "%INSTALL_DIR%\"

echo 创建桌面快捷方式...
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%DESKTOP_SHORTCUT%'); $Shortcut.TargetPath = '%INSTALL_DIR%\SimpleDesktop.exe'; $Shortcut.IconLocation = '%INSTALL_DIR%\ic.ico'; $Shortcut.Save()"

echo 创建开始菜单快捷方式...
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%STARTMENU_SHORTCUT%'); $Shortcut.TargetPath = '%INSTALL_DIR%\SimpleDesktop.exe'; $Shortcut.IconLocation = '%INSTALL_DIR%\ic.ico'; $Shortcut.Save()"

echo.
echo 安装完成！
echo 程序已安装到: %INSTALL_DIR%
echo 桌面快捷方式已创建
echo.
pause
