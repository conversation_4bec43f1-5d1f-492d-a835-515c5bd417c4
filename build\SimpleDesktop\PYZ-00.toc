('D:\\withEverything\\build\\SimpleDesktop\\PYZ-00.pyz',
 [('PySide6', 'D:\\ana\\Lib\\site-packages\\PySide6\\__init__.py', 'PYMODULE'),
  ('PySide6.QtAsyncio',
   'D:\\ana\\Lib\\site-packages\\PySide6\\QtAsyncio\\__init__.py',
   'PYMODULE'),
  ('PySide6.QtAsyncio.events',
   'D:\\ana\\Lib\\site-packages\\PySide6\\QtAsyncio\\events.py',
   'PYMODULE'),
  ('PySide6.QtAsyncio.futures',
   'D:\\ana\\Lib\\site-packages\\PySide6\\QtAsyncio\\futures.py',
   'PYMODULE'),
  ('PySide6.QtAsyncio.tasks',
   'D:\\ana\\Lib\\site-packages\\PySide6\\QtAsyncio\\tasks.py',
   'PYMODULE'),
  ('PySide6._config',
   'D:\\ana\\Lib\\site-packages\\PySide6\\_config.py',
   'PYMODULE'),
  ('PySide6._git_pyside_version',
   'D:\\ana\\Lib\\site-packages\\PySide6\\_git_pyside_version.py',
   'PYMODULE'),
  ('PySide6.scripts',
   'D:\\ana\\Lib\\site-packages\\PySide6\\scripts\\__init__.py',
   'PYMODULE'),
  ('PySide6.scripts.deploy',
   'D:\\ana\\Lib\\site-packages\\PySide6\\scripts\\deploy.py',
   'PYMODULE'),
  ('PySide6.scripts.metaobjectdump',
   'D:\\ana\\Lib\\site-packages\\PySide6\\scripts\\metaobjectdump.py',
   'PYMODULE'),
  ('PySide6.scripts.project',
   'D:\\ana\\Lib\\site-packages\\PySide6\\scripts\\project.py',
   'PYMODULE'),
  ('PySide6.scripts.project_lib',
   'D:\\ana\\Lib\\site-packages\\PySide6\\scripts\\project_lib\\__init__.py',
   'PYMODULE'),
  ('PySide6.scripts.project_lib.design_studio_project',
   'D:\\ana\\Lib\\site-packages\\PySide6\\scripts\\project_lib\\design_studio_project.py',
   'PYMODULE'),
  ('PySide6.scripts.project_lib.newproject',
   'D:\\ana\\Lib\\site-packages\\PySide6\\scripts\\project_lib\\newproject.py',
   'PYMODULE'),
  ('PySide6.scripts.project_lib.project_data',
   'D:\\ana\\Lib\\site-packages\\PySide6\\scripts\\project_lib\\project_data.py',
   'PYMODULE'),
  ('PySide6.scripts.project_lib.pyproject_json',
   'D:\\ana\\Lib\\site-packages\\PySide6\\scripts\\project_lib\\pyproject_json.py',
   'PYMODULE'),
  ('PySide6.scripts.project_lib.pyproject_parse_result',
   'D:\\ana\\Lib\\site-packages\\PySide6\\scripts\\project_lib\\pyproject_parse_result.py',
   'PYMODULE'),
  ('PySide6.scripts.project_lib.pyproject_toml',
   'D:\\ana\\Lib\\site-packages\\PySide6\\scripts\\project_lib\\pyproject_toml.py',
   'PYMODULE'),
  ('PySide6.scripts.project_lib.utils',
   'D:\\ana\\Lib\\site-packages\\PySide6\\scripts\\project_lib\\utils.py',
   'PYMODULE'),
  ('PySide6.scripts.pyside_tool',
   'D:\\ana\\Lib\\site-packages\\PySide6\\scripts\\pyside_tool.py',
   'PYMODULE'),
  ('PySide6.scripts.qml',
   'D:\\ana\\Lib\\site-packages\\PySide6\\scripts\\qml.py',
   'PYMODULE'),
  ('PySide6.scripts.qtpy2cpp',
   'D:\\ana\\Lib\\site-packages\\PySide6\\scripts\\qtpy2cpp.py',
   'PYMODULE'),
  ('PySide6.support',
   'D:\\ana\\Lib\\site-packages\\PySide6\\support\\__init__.py',
   'PYMODULE'),
  ('PySide6.support.deprecated',
   'D:\\ana\\Lib\\site-packages\\PySide6\\support\\deprecated.py',
   'PYMODULE'),
  ('PySide6.support.generate_pyi',
   'D:\\ana\\Lib\\site-packages\\PySide6\\support\\generate_pyi.py',
   'PYMODULE'),
  ('__future__', 'D:\\ana\\Lib\\__future__.py', 'PYMODULE'),
  ('_aix_support', 'D:\\ana\\Lib\\_aix_support.py', 'PYMODULE'),
  ('_compat_pickle', 'D:\\ana\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('_compression', 'D:\\ana\\Lib\\_compression.py', 'PYMODULE'),
  ('_py_abc', 'D:\\ana\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydatetime', 'D:\\ana\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\ana\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('_pyi_rth_utils',
   'D:\\ana\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'D:\\ana\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_strptime', 'D:\\ana\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local', 'D:\\ana\\Lib\\_threading_local.py', 'PYMODULE'),
  ('argparse', 'D:\\ana\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'D:\\ana\\Lib\\ast.py', 'PYMODULE'),
  ('asyncio', 'D:\\ana\\Lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.base_events', 'D:\\ana\\Lib\\asyncio\\base_events.py', 'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\ana\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\ana\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks', 'D:\\ana\\Lib\\asyncio\\base_tasks.py', 'PYMODULE'),
  ('asyncio.constants', 'D:\\ana\\Lib\\asyncio\\constants.py', 'PYMODULE'),
  ('asyncio.coroutines', 'D:\\ana\\Lib\\asyncio\\coroutines.py', 'PYMODULE'),
  ('asyncio.events', 'D:\\ana\\Lib\\asyncio\\events.py', 'PYMODULE'),
  ('asyncio.exceptions', 'D:\\ana\\Lib\\asyncio\\exceptions.py', 'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\ana\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures', 'D:\\ana\\Lib\\asyncio\\futures.py', 'PYMODULE'),
  ('asyncio.locks', 'D:\\ana\\Lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.log', 'D:\\ana\\Lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.mixins', 'D:\\ana\\Lib\\asyncio\\mixins.py', 'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\ana\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols', 'D:\\ana\\Lib\\asyncio\\protocols.py', 'PYMODULE'),
  ('asyncio.queues', 'D:\\ana\\Lib\\asyncio\\queues.py', 'PYMODULE'),
  ('asyncio.runners', 'D:\\ana\\Lib\\asyncio\\runners.py', 'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\ana\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto', 'D:\\ana\\Lib\\asyncio\\sslproto.py', 'PYMODULE'),
  ('asyncio.staggered', 'D:\\ana\\Lib\\asyncio\\staggered.py', 'PYMODULE'),
  ('asyncio.streams', 'D:\\ana\\Lib\\asyncio\\streams.py', 'PYMODULE'),
  ('asyncio.subprocess', 'D:\\ana\\Lib\\asyncio\\subprocess.py', 'PYMODULE'),
  ('asyncio.taskgroups', 'D:\\ana\\Lib\\asyncio\\taskgroups.py', 'PYMODULE'),
  ('asyncio.tasks', 'D:\\ana\\Lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.threads', 'D:\\ana\\Lib\\asyncio\\threads.py', 'PYMODULE'),
  ('asyncio.timeouts', 'D:\\ana\\Lib\\asyncio\\timeouts.py', 'PYMODULE'),
  ('asyncio.transports', 'D:\\ana\\Lib\\asyncio\\transports.py', 'PYMODULE'),
  ('asyncio.trsock', 'D:\\ana\\Lib\\asyncio\\trsock.py', 'PYMODULE'),
  ('asyncio.unix_events', 'D:\\ana\\Lib\\asyncio\\unix_events.py', 'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\ana\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\ana\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('base64', 'D:\\ana\\Lib\\base64.py', 'PYMODULE'),
  ('bisect', 'D:\\ana\\Lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'D:\\ana\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'D:\\ana\\Lib\\calendar.py', 'PYMODULE'),
  ('concurrent', 'D:\\ana\\Lib\\concurrent\\__init__.py', 'PYMODULE'),
  ('concurrent.futures',
   'D:\\ana\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\ana\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\ana\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\ana\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('contextlib', 'D:\\ana\\Lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'D:\\ana\\Lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'D:\\ana\\Lib\\copy.py', 'PYMODULE'),
  ('csv', 'D:\\ana\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'D:\\ana\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._endian', 'D:\\ana\\Lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('ctypes.wintypes', 'D:\\ana\\Lib\\ctypes\\wintypes.py', 'PYMODULE'),
  ('dataclasses', 'D:\\ana\\Lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'D:\\ana\\Lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'D:\\ana\\Lib\\decimal.py', 'PYMODULE'),
  ('dis', 'D:\\ana\\Lib\\dis.py', 'PYMODULE'),
  ('email', 'D:\\ana\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\ana\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\ana\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr', 'D:\\ana\\Lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('email._policybase', 'D:\\ana\\Lib\\email\\_policybase.py', 'PYMODULE'),
  ('email.base64mime', 'D:\\ana\\Lib\\email\\base64mime.py', 'PYMODULE'),
  ('email.charset', 'D:\\ana\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'D:\\ana\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders', 'D:\\ana\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.errors', 'D:\\ana\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser', 'D:\\ana\\Lib\\email\\feedparser.py', 'PYMODULE'),
  ('email.generator', 'D:\\ana\\Lib\\email\\generator.py', 'PYMODULE'),
  ('email.header', 'D:\\ana\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'D:\\ana\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators', 'D:\\ana\\Lib\\email\\iterators.py', 'PYMODULE'),
  ('email.message', 'D:\\ana\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.parser', 'D:\\ana\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'D:\\ana\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime', 'D:\\ana\\Lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.utils', 'D:\\ana\\Lib\\email\\utils.py', 'PYMODULE'),
  ('fnmatch', 'D:\\ana\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'D:\\ana\\Lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'D:\\ana\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'D:\\ana\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'D:\\ana\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'D:\\ana\\Lib\\gettext.py', 'PYMODULE'),
  ('gzip', 'D:\\ana\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'D:\\ana\\Lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'D:\\ana\\Lib\\hmac.py', 'PYMODULE'),
  ('http', 'D:\\ana\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'D:\\ana\\Lib\\http\\client.py', 'PYMODULE'),
  ('http.cookiejar', 'D:\\ana\\Lib\\http\\cookiejar.py', 'PYMODULE'),
  ('importlib', 'D:\\ana\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._abc', 'D:\\ana\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\ana\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\ana\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc', 'D:\\ana\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery', 'D:\\ana\\Lib\\importlib\\machinery.py', 'PYMODULE'),
  ('importlib.metadata',
   'D:\\ana\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\ana\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\ana\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\ana\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\ana\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\ana\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\ana\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers', 'D:\\ana\\Lib\\importlib\\readers.py', 'PYMODULE'),
  ('importlib.resources',
   'D:\\ana\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\ana\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\ana\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\ana\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'D:\\ana\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\ana\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\ana\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util', 'D:\\ana\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('inspect', 'D:\\ana\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'D:\\ana\\Lib\\ipaddress.py', 'PYMODULE'),
  ('json', 'D:\\ana\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder', 'D:\\ana\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.encoder', 'D:\\ana\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.scanner', 'D:\\ana\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('logging', 'D:\\ana\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('lzma', 'D:\\ana\\Lib\\lzma.py', 'PYMODULE'),
  ('mimetypes', 'D:\\ana\\Lib\\mimetypes.py', 'PYMODULE'),
  ('multiprocessing', 'D:\\ana\\Lib\\multiprocessing\\__init__.py', 'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\ana\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'D:\\ana\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\ana\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\ana\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\ana\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\ana\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\ana\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\ana\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\ana\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\ana\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\ana\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\ana\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\ana\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\ana\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\ana\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\ana\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\ana\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\ana\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\ana\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\ana\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\ana\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\ana\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'D:\\ana\\Lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'D:\\ana\\Lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'D:\\ana\\Lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'D:\\ana\\Lib\\opcode.py', 'PYMODULE'),
  ('pathlib', 'D:\\ana\\Lib\\pathlib.py', 'PYMODULE'),
  ('pickle', 'D:\\ana\\Lib\\pickle.py', 'PYMODULE'),
  ('pkgutil', 'D:\\ana\\Lib\\pkgutil.py', 'PYMODULE'),
  ('pprint', 'D:\\ana\\Lib\\pprint.py', 'PYMODULE'),
  ('py_compile', 'D:\\ana\\Lib\\py_compile.py', 'PYMODULE'),
  ('queue', 'D:\\ana\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'D:\\ana\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\ana\\Lib\\random.py', 'PYMODULE'),
  ('runpy', 'D:\\ana\\Lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'D:\\ana\\Lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'D:\\ana\\Lib\\selectors.py', 'PYMODULE'),
  ('shiboken6',
   'D:\\ana\\Lib\\site-packages\\shiboken6\\__init__.py',
   'PYMODULE'),
  ('shutil', 'D:\\ana\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\ana\\Lib\\signal.py', 'PYMODULE'),
  ('simple_desktop',
   'D:\\withEverything\\simple_desktop\\__init__.py',
   'PYMODULE'),
  ('simple_desktop.app',
   'D:\\withEverything\\simple_desktop\\app.py',
   'PYMODULE'),
  ('simple_desktop.core',
   'D:\\withEverything\\simple_desktop\\core\\__init__.py',
   'PYMODULE'),
  ('simple_desktop.core.alt_hotkey',
   'D:\\withEverything\\simple_desktop\\core\\alt_hotkey.py',
   'PYMODULE'),
  ('simple_desktop.core.config',
   'D:\\withEverything\\simple_desktop\\core\\config.py',
   'PYMODULE'),
  ('simple_desktop.core.everything_sdk',
   'D:\\withEverything\\simple_desktop\\core\\everything_sdk.py',
   'PYMODULE'),
  ('simple_desktop.core.profile_manager',
   'D:\\withEverything\\simple_desktop\\core\\profile_manager.py',
   'PYMODULE'),
  ('simple_desktop.search',
   'D:\\withEverything\\simple_desktop\\search\\__init__.py',
   'PYMODULE'),
  ('simple_desktop.search.engine',
   'D:\\withEverything\\simple_desktop\\search\\engine.py',
   'PYMODULE'),
  ('simple_desktop.search.filters',
   'D:\\withEverything\\simple_desktop\\search\\filters.py',
   'PYMODULE'),
  ('simple_desktop.search.models',
   'D:\\withEverything\\simple_desktop\\search\\models.py',
   'PYMODULE'),
  ('simple_desktop.ui',
   'D:\\withEverything\\simple_desktop\\ui\\__init__.py',
   'PYMODULE'),
  ('simple_desktop.ui.dialogs',
   'D:\\withEverything\\simple_desktop\\ui\\dialogs.py',
   'PYMODULE'),
  ('simple_desktop.ui.search_window',
   'D:\\withEverything\\simple_desktop\\ui\\search_window.py',
   'PYMODULE'),
  ('socket', 'D:\\ana\\Lib\\socket.py', 'PYMODULE'),
  ('ssl', 'D:\\ana\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'D:\\ana\\Lib\\statistics.py', 'PYMODULE'),
  ('string', 'D:\\ana\\Lib\\string.py', 'PYMODULE'),
  ('stringprep', 'D:\\ana\\Lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'D:\\ana\\Lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig', 'D:\\ana\\Lib\\sysconfig.py', 'PYMODULE'),
  ('tarfile', 'D:\\ana\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'D:\\ana\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'D:\\ana\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'D:\\ana\\Lib\\threading.py', 'PYMODULE'),
  ('token', 'D:\\ana\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\ana\\Lib\\tokenize.py', 'PYMODULE'),
  ('tomllib', 'D:\\ana\\Lib\\tomllib\\__init__.py', 'PYMODULE'),
  ('tomllib._parser', 'D:\\ana\\Lib\\tomllib\\_parser.py', 'PYMODULE'),
  ('tomllib._re', 'D:\\ana\\Lib\\tomllib\\_re.py', 'PYMODULE'),
  ('tomllib._types', 'D:\\ana\\Lib\\tomllib\\_types.py', 'PYMODULE'),
  ('tracemalloc', 'D:\\ana\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('typing', 'D:\\ana\\Lib\\typing.py', 'PYMODULE'),
  ('urllib', 'D:\\ana\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.error', 'D:\\ana\\Lib\\urllib\\error.py', 'PYMODULE'),
  ('urllib.parse', 'D:\\ana\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('urllib.request', 'D:\\ana\\Lib\\urllib\\request.py', 'PYMODULE'),
  ('urllib.response', 'D:\\ana\\Lib\\urllib\\response.py', 'PYMODULE'),
  ('win32con',
   'D:\\ana\\Lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('xml', 'D:\\ana\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.etree', 'D:\\ana\\Lib\\xml\\etree\\__init__.py', 'PYMODULE'),
  ('xml.etree.ElementInclude',
   'D:\\ana\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'D:\\ana\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'D:\\ana\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'D:\\ana\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers', 'D:\\ana\\Lib\\xml\\parsers\\__init__.py', 'PYMODULE'),
  ('xml.parsers.expat', 'D:\\ana\\Lib\\xml\\parsers\\expat.py', 'PYMODULE'),
  ('xml.sax', 'D:\\ana\\Lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax._exceptions', 'D:\\ana\\Lib\\xml\\sax\\_exceptions.py', 'PYMODULE'),
  ('xml.sax.expatreader', 'D:\\ana\\Lib\\xml\\sax\\expatreader.py', 'PYMODULE'),
  ('xml.sax.handler', 'D:\\ana\\Lib\\xml\\sax\\handler.py', 'PYMODULE'),
  ('xml.sax.saxutils', 'D:\\ana\\Lib\\xml\\sax\\saxutils.py', 'PYMODULE'),
  ('xml.sax.xmlreader', 'D:\\ana\\Lib\\xml\\sax\\xmlreader.py', 'PYMODULE'),
  ('xmlrpc', 'D:\\ana\\Lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xmlrpc.client', 'D:\\ana\\Lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('zipfile', 'D:\\ana\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path', 'D:\\ana\\Lib\\zipfile\\_path\\__init__.py', 'PYMODULE'),
  ('zipfile._path.glob', 'D:\\ana\\Lib\\zipfile\\_path\\glob.py', 'PYMODULE'),
  ('zipimport', 'D:\\ana\\Lib\\zipimport.py', 'PYMODULE')])
